//@version=5
indicator("Volume Tape Histogram", "Tape", true, max_bars_back=500)

// Settings
lookback = input.int(14, "Lookback Period", minval=1, maxval=100, tooltip="Number of bars to look back for smoothing")
smoothing = input.int(3, "Smoothing Length", minval=1, maxval=20, tooltip="Length for smoothing the volume bars")

// Colors for the histogram
upColor = color.blue     // Blue for upticks
dnColor = color.red      // Red for downticks

// Simple approach - use current bar data
var float prevClose = na
var float upVolume = 0.0
var float dnVolume = 0.0

// Calculate price change over lookback period
priceChange = ta.change(close, lookback)
volumeToUse = volume

// Determine direction based on price change over lookback period
if priceChange > 0
    // Price trend is up over lookback period
    upVolume := volumeToUse
    dnVolume := 0
else if priceChange < 0
    // Price trend is down over lookback period
    upVolume := 0
    dnVolume := volumeToUse
else
    // No significant price change
    upVolume := 0
    dnVolume := 0

// Apply smoothing to the volume data
smoothedUpVolume = ta.sma(upVolume, smoothing)
smoothedDnVolume = ta.sma(dnVolume, smoothing)

// Calculate net delta - show only the dominant side
netDelta = smoothedUpVolume - smoothedDnVolume
finalUpVolume = netDelta > 0 ? netDelta : 0
finalDnVolume = netDelta < 0 ? math.abs(netDelta) : 0

// Plot only the dominant side
plot(finalUpVolume, color=upColor, style=plot.style_histogram, linewidth=2, title="Net Up Volume")
plot(-finalDnVolume, color=dnColor, style=plot.style_histogram, linewidth=2, title="Net Down Volume")

// Add a zero line for reference
hline(0, "Zero Line", color=color.gray, linestyle=hline.style_dashed)
