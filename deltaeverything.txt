//@version=6
indicator("Delta Everything", overlay=true, max_labels_count=500, max_lines_count=500, max_boxes_count=500)

import TradingView/ta/8

// Volume Profile Settings
vpGroup = "Volume Profile"
vpEnabled = input.bool(true, "Show Volume Profile", group=vpGroup)
vpMode = input.string("Delta Profile", "Profile Mode", options=["Volume Profile", "Delta Profile"], group=vpGroup, tooltip="Choose between volume-based or delta-based profile")
vpRowSize = input.float(25.0, "Row Size (Ticks)", minval=1.0, maxval=1000.0, step=1.0, group=vpGroup, tooltip="Size of each row in ticks (like TradingView default)")
vpLookback = input.int(100, "Lookback Period", minval=10, maxval=500, group=vpGroup, tooltip="Number of bars to calculate volume profile")

// Fixed values (no user inputs)
vpProfileWidth = 25  // Fixed width
vpOffset = 50        // Fixed offset

// CVD Divergence Settings
divGroup = "CVD Divergences"
divEnabled = input.bool(true, "Show CVD Divergences", group=divGroup)
divLookback = input.int(5, "Pivot Lookback", minval=2, maxval=15, group=divGroup, tooltip="Number of bars to look back for pivot detection")
divMinBars = input.int(10, "Minimum Bars Between Pivots", minval=5, maxval=50, group=divGroup, tooltip="Minimum number of bars required between pivot points")
divShowText = input.bool(true, "Show Text Labels", group=divGroup, tooltip="Show or hide text labels on divergence lines")
divTextSize = input.string("Small", "Text Size", options=["Tiny", "Small", "Normal", "Large", "Huge"], group=divGroup, tooltip="Size of divergence text labels")

// VWAP Settings
vwapGroup = "VWAP Settings"
vwapEnabled = input.bool(true, "Show VWAP", group=vwapGroup)
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group=vwapGroup, display = display.data_window)
var anchor = input.string(defval = "Session", title="Anchor Period",
 options=["Session", "Week", "Month", "Quarter", "Year", "Decade", "Century", "Earnings", "Dividends", "Splits"], group=vwapGroup)
vwapSrc = input(title = "Source", defval = hlc3, group=vwapGroup, display = display.data_window)
vwapOffset = input.int(0, title="Offset", group=vwapGroup, minval=0, display = display.data_window)

BANDS_GROUP = "VWAP Bands Settings"
CALC_MODE_TOOLTIP = "Determines the units used to calculate the distance of the bands. When 'Percentage' is selected, a multiplier of 1 means 1%."
calcModeInput = input.string("Standard Deviation", "Bands Calculation Mode", options = ["Standard Deviation", "Percentage"], group = BANDS_GROUP, tooltip = CALC_MODE_TOOLTIP, display = display.data_window)
showBand_1 = input(true, title = "", group = BANDS_GROUP, inline = "band_1", display = display.data_window)
bandMult_1 = input.float(1.0, title = "Bands Multiplier #1", group = BANDS_GROUP, inline = "band_1", step = 0.5, minval=0, display = display.data_window)
showBand_2 = input(false, title = "", group = BANDS_GROUP, inline = "band_2", display = display.data_window)
bandMult_2 = input.float(2.0, title = "Bands Multiplier #2", group = BANDS_GROUP, inline = "band_2", step = 0.5, minval=0, display = display.data_window)
showBand_3 = input(false, title = "", group = BANDS_GROUP, inline = "band_3", display = display.data_window)
bandMult_3 = input.float(3.0, title = "Bands Multiplier #3", group = BANDS_GROUP, inline = "band_3", step = 0.5, minval=0, display = display.data_window)

// VWAP Style Settings
STYLE_GROUP = "VWAP Style Settings"
vwapColor = input.color(#2962FF, "VWAP Color", group = STYLE_GROUP)
vwapLineWidth = input.int(1, "VWAP Line Width", minval=1, maxval=4, group = STYLE_GROUP)
band1Color = input.color(color.green, "Band #1 Color", group = STYLE_GROUP, inline = "style_1")
band1Transparency = input.int(95, "Fill Transparency", minval=0, maxval=100, group = STYLE_GROUP, inline = "style_1")
band2Color = input.color(color.olive, "Band #2 Color", group = STYLE_GROUP, inline = "style_2")
band2Transparency = input.int(95, "Fill Transparency", minval=0, maxval=100, group = STYLE_GROUP, inline = "style_2")
band3Color = input.color(color.teal, "Band #3 Color", group = STYLE_GROUP, inline = "style_3")
band3Transparency = input.int(95, "Fill Transparency", minval=0, maxval=100, group = STYLE_GROUP, inline = "style_3")

// Function to convert text size string to size constant
getTextSize() =>
    switch divTextSize
        "Tiny" => size.tiny
        "Small" => size.small
        "Normal" => size.normal
        "Large" => size.large
        "Huge" => size.huge
        => size.small

// VWAP Calculation
cumVolume = ta.cum(volume)
if barstate.islast and cumVolume == 0
    runtime.error("No volume is provided by the data vendor.")

new_earnings = request.earnings(syminfo.tickerid, earnings.actual, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_dividends = request.dividends(syminfo.tickerid, dividends.gross, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_split = request.splits(syminfo.tickerid, splits.denominator, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)

isNewPeriod = switch anchor
	"Earnings"  => not na(new_earnings)
	"Dividends" => not na(new_dividends)
	"Splits"    => not na(new_split)
	"Session"   => timeframe.change("D")
	"Week"      => timeframe.change("W")
	"Month"     => timeframe.change("M")
	"Quarter"   => timeframe.change("3M")
	"Year"      => timeframe.change("12M")
	"Decade"    => timeframe.change("12M") and year % 10 == 0
	"Century"   => timeframe.change("12M") and year % 100 == 0
	=> false

isEsdAnchor = anchor == "Earnings" or anchor == "Dividends" or anchor == "Splits"
if na(vwapSrc[1]) and not isEsdAnchor
	isNewPeriod := true

float vwapValue = na
float upperBandValue1 = na
float lowerBandValue1 = na
float upperBandValue2 = na
float lowerBandValue2 = na
float upperBandValue3 = na
float lowerBandValue3 = na

if vwapEnabled and not (hideonDWM and timeframe.isdwm)
    [_vwap, _stdevUpper, _] = ta.vwap(vwapSrc, isNewPeriod, 1)
	vwapValue := _vwap
    stdevAbs = _stdevUpper - _vwap
	bandBasis = calcModeInput == "Standard Deviation" ? stdevAbs : _vwap * 0.01
	upperBandValue1 := _vwap + bandBasis * bandMult_1
	lowerBandValue1 := _vwap - bandBasis * bandMult_1
	upperBandValue2 := _vwap + bandBasis * bandMult_2
	lowerBandValue2 := _vwap - bandBasis * bandMult_2
	upperBandValue3 := _vwap + bandBasis * bandMult_3
	lowerBandValue3 := _vwap - bandBasis * bandMult_3

// Delta intensity settings
intensityMultiplier = input.float(1.0, "Delta Sensitivity", minval=0.1, maxval=5.0, step=0.1, tooltip="Adjust the sensitivity of delta detection. Higher values make smaller deltas more visible.")

// Standard deviation calculation settings
stdDevGroup = "Standard Deviation Calculation"
stdDevMode = input.string("All Data", "Calculation Mode", options=["All Data", "1 Hour", "15 Minutes", "5 Minutes", "1 Minute"], group=stdDevGroup, tooltip="Choose the timeframe for standard deviation calculation")
resetPeriod = input.int(24, "Reset Period (Hours)", minval=1, maxval=168, group=stdDevGroup, tooltip="Reset statistics every X hours (only for time-based modes)")



// Color customization settings
bullishColorGroup = "Bullish Colors (Positive Delta)"
veryLightBullishColor = input.color(#90bff9, "Very Light (0-25%)", group=bullishColorGroup)
lightBullishColor = input.color(#5b9cf6, "Light (25-50%)", group=bullishColorGroup)
mediumBullishColor = input.color(#1848cc, "Normal (50-75%)", group=bullishColorGroup)
darkBullishColor = input.color(#0c3299, "Dark (75%+)", group=bullishColorGroup)

bearishColorGroup = "Bearish Colors (Negative Delta)"
veryLightBearishColor = input.color(#faa1a4, "Very Light (0-25%)", group=bearishColorGroup)
lightBearishColor = input.color(#f77c80, "Light (25-50%)", group=bearishColorGroup)
mediumBearishColor = input.color(#f23645, "Normal (50-75%)", group=bearishColorGroup)
darkBearishColor = input.color(#801922, "Dark (75%+)", group=bearishColorGroup)



// Color threshold display (for reference)
thresholdInfo = "Color Thresholds (based on Standard Deviation):\n• <25%: Very Light Blue/Red\n• 25-50%: Light Blue/Red (darker)\n• 50-75%: Normal Blue/Red\n• >75%: Dark Blue/Red\n\nCalculations use all available data"
showThresholds = input.bool(false, "Show Color Thresholds", tooltip=thresholdInfo)

// Determine the appropriate lower timeframe based on current chart timeframe
var lowerTimeframe = switch
    timeframe.isseconds     => "1S"
    timeframe.isintraday    => "1"
    timeframe.isdaily       => "5"
    => "60"

// Get volume delta data
[openVolume, maxVolume, minVolume, lastVolume] = ta.requestVolumeDelta(lowerTimeframe)

// Calculate CVD (Cumulative Volume Delta) for divergence detection
var float cvd = 0.0
if not na(lastVolume)
    cvd += lastVolume

// Variables to track statistics
var float positiveDeltaSum = 0.0
var float negativeDeltaSum = 0.0
var float positiveDeltaSumSq = 0.0
var float negativeDeltaSumSq = 0.0
var int positiveCount = 0
var int negativeCount = 0
var int lastResetTime = 0

// Function to get reset interval in milliseconds
getResetInterval() =>
    switch stdDevMode
        "1 Hour" => 60 * 60 * 1000
        "15 Minutes" => 15 * 60 * 1000
        "5 Minutes" => 5 * 60 * 1000
        "1 Minute" => 1 * 60 * 1000
        => 0  // All Data - never reset

// Check if we need to reset statistics
resetInterval = getResetInterval()
currentTime = time
shouldReset = resetInterval > 0 and (currentTime - lastResetTime) >= resetInterval

// Reset statistics if needed
if shouldReset or (stdDevMode == "All Data" and lastResetTime == 0)
    positiveDeltaSum := 0.0
    negativeDeltaSum := 0.0
    positiveDeltaSumSq := 0.0
    negativeDeltaSumSq := 0.0
    positiveCount := 0
    negativeCount := 0
    lastResetTime := currentTime

// Update statistics with current bar data
if not na(lastVolume)
    if lastVolume > 0
        positiveDeltaSum += lastVolume
        positiveDeltaSumSq += lastVolume * lastVolume
        positiveCount += 1
    else if lastVolume < 0
        absNegativeDelta = math.abs(lastVolume)
        negativeDeltaSum += absNegativeDelta
        negativeDeltaSumSq += absNegativeDelta * absNegativeDelta
        negativeCount += 1

// Calculate means and standard deviations
positiveMean = positiveCount > 0 ? positiveDeltaSum / positiveCount : 0
negativeMean = negativeCount > 0 ? negativeDeltaSum / negativeCount : 0

positiveVariance = positiveCount > 1 ? (positiveDeltaSumSq - (positiveDeltaSum * positiveDeltaSum / positiveCount)) / (positiveCount - 1) : 0
negativeVariance = negativeCount > 1 ? (negativeDeltaSumSq - (negativeDeltaSum * negativeDeltaSum / negativeCount)) / (negativeCount - 1) : 0

positiveStdDev = positiveVariance > 0 ? math.sqrt(positiveVariance) : 0
negativeStdDev = negativeVariance > 0 ? math.sqrt(negativeVariance) : 0

// Normalize current delta based on its respective distribution
normalizedDelta = if lastVolume > 0 and positiveStdDev > 0
    (lastVolume - positiveMean) / positiveStdDev
else if lastVolume < 0 and negativeStdDev > 0
    (math.abs(lastVolume) - negativeMean) / negativeStdDev
else
    0

// Apply intensity multiplier and clamp to reasonable range
adjustedNormalizedDelta = math.abs(normalizedDelta * intensityMultiplier)
clampedDelta = math.min(adjustedNormalizedDelta, 3.0)  // Clamp to 3 standard deviations

// Function to get color based on normalized standard deviation
getColorByStdDev(normalizedStdDev, isPositive) =>
    // Convert normalized std dev to percentage thresholds
    // 0.5 std dev = 25%, 1.0 std dev = 50%, 1.5 std dev = 75%, 2.0+ std dev = 100%
    stdDevPercent = normalizedStdDev / 2.0  // Scale to make thresholds more reasonable

    if stdDevPercent < 0.25
        isPositive ? veryLightBullishColor : veryLightBearishColor  // Under 25% = very light
    else if stdDevPercent < 0.50
        isPositive ? lightBullishColor : lightBearishColor  // 25-50% = light (darker than under 25%)
    else if stdDevPercent < 0.75
        isPositive ? mediumBullishColor : mediumBearishColor  // 50-75% = normal blue/red
    else
        isPositive ? darkBullishColor : darkBearishColor  // 75%+ = dark blue/red



// Determine candle color based on normalized standard deviation
candleColor = getColorByStdDev(clampedDelta, lastVolume > 0)

// Color the existing candles
barcolor(candleColor, title="Delta Heatmap")

// CVD Divergence Detection
// Pivot detection functions (only look back in history, no future bars)
isPivotHigh(src, leftBars) =>
    if bar_index < leftBars
        false
    else
        pivotHigh = true
        for i = 1 to leftBars
            if src[i] >= src[0]
                pivotHigh := false
                break
        pivotHigh

isPivotLow(src, leftBars) =>
    if bar_index < leftBars
        false
    else
        pivotLow = true
        for i = 1 to leftBars
            if src[i] <= src[0]
                pivotLow := false
                break
        pivotLow

// Detect pivots (only using historical data)
priceHigh = isPivotHigh(high, divLookback)
priceLow = isPivotLow(low, divLookback)
cvdHigh = isPivotHigh(cvd, divLookback)
cvdLow = isPivotLow(cvd, divLookback)

// Store pivot data
type PivotPoint
    int barIndex
    float priceValue
    float cvdValue

var array<PivotPoint> priceHighs = array.new<PivotPoint>()
var array<PivotPoint> priceLows = array.new<PivotPoint>()
var array<PivotPoint> cvdHighs = array.new<PivotPoint>()
var array<PivotPoint> cvdLows = array.new<PivotPoint>()

// Add new pivots when detected
if divEnabled
    // Add price pivots
    if priceHigh
        currentBarIndex = bar_index
        array.push(priceHighs, PivotPoint.new(currentBarIndex, high, cvd))
        // Keep only recent pivots
        if array.size(priceHighs) > 10
            array.shift(priceHighs)

    if priceLow
        currentBarIndex = bar_index
        array.push(priceLows, PivotPoint.new(currentBarIndex, low, cvd))
        // Keep only recent pivots
        if array.size(priceLows) > 10
            array.shift(priceLows)

    // Add CVD pivots
    if cvdHigh
        currentBarIndex = bar_index
        array.push(cvdHighs, PivotPoint.new(currentBarIndex, high, cvd))
        // Keep only recent pivots
        if array.size(cvdHighs) > 10
            array.shift(cvdHighs)

    if cvdLow
        currentBarIndex = bar_index
        array.push(cvdLows, PivotPoint.new(currentBarIndex, low, cvd))
        // Keep only recent pivots
        if array.size(cvdLows) > 10
            array.shift(cvdLows)

// Divergence detection and drawing
if divEnabled
    // Check for divergences between the last two price highs
    if array.size(priceHighs) >= 2
        lastPriceHigh = array.get(priceHighs, array.size(priceHighs) - 1)
        prevPriceHigh = array.get(priceHighs, array.size(priceHighs) - 2)

        // Check if we have enough bars between pivots
        if lastPriceHigh.barIndex - prevPriceHigh.barIndex >= divMinBars
            // Check for uptrend exhaustion: higher high in price but lower high in CVD
            if lastPriceHigh.priceValue > prevPriceHigh.priceValue and
               lastPriceHigh.cvdValue < prevPriceHigh.cvdValue
                // Calculate center position for text
                centerBarIndex = math.round((prevPriceHigh.barIndex + lastPriceHigh.barIndex) / 2)
                centerPrice = (prevPriceHigh.priceValue + lastPriceHigh.priceValue) / 2

                // Draw solid divergence line
                line.new(prevPriceHigh.barIndex, prevPriceHigh.priceValue, lastPriceHigh.barIndex, lastPriceHigh.priceValue,
                         color=color.white, width=2, style=line.style_solid)
                // Add centered text on the line (if enabled)
                if divShowText
                    label.new(centerBarIndex, centerPrice, "UPTREND EXHAUSTION",
                             color=color.new(color.white, 100), textcolor=color.white, style=label.style_none, size=getTextSize())

            // Check for uptrend absorption: lower high in price but higher high in CVD
            if lastPriceHigh.priceValue < prevPriceHigh.priceValue and
               lastPriceHigh.cvdValue > prevPriceHigh.cvdValue
                // Calculate center position for text
                centerBarIndex = math.round((prevPriceHigh.barIndex + lastPriceHigh.barIndex) / 2)
                centerPrice = (prevPriceHigh.priceValue + lastPriceHigh.priceValue) / 2

                // Draw solid divergence line
                line.new(prevPriceHigh.barIndex, prevPriceHigh.priceValue, lastPriceHigh.barIndex, lastPriceHigh.priceValue,
                         color=color.white, width=2, style=line.style_solid)
                // Add centered text on the line (if enabled)
                if divShowText
                    label.new(centerBarIndex, centerPrice, "UPTREND ABSORPTION",
                             color=color.new(color.white, 100), textcolor=color.white, style=label.style_none, size=getTextSize())

    // Check for divergences between the last two price lows
    if array.size(priceLows) >= 2
        lastPriceLow = array.get(priceLows, array.size(priceLows) - 1)
        prevPriceLow = array.get(priceLows, array.size(priceLows) - 2)

        // Check if we have enough bars between pivots
        if lastPriceLow.barIndex - prevPriceLow.barIndex >= divMinBars
            // Check for downtrend exhaustion: lower low in price but higher low in CVD
            if lastPriceLow.priceValue < prevPriceLow.priceValue and
               lastPriceLow.cvdValue > prevPriceLow.cvdValue
                // Calculate center position for text
                centerBarIndex = math.round((prevPriceLow.barIndex + lastPriceLow.barIndex) / 2)
                centerPrice = (prevPriceLow.priceValue + lastPriceLow.priceValue) / 2

                // Draw solid divergence line
                line.new(prevPriceLow.barIndex, prevPriceLow.priceValue, lastPriceLow.barIndex, lastPriceLow.priceValue,
                         color=color.white, width=2, style=line.style_solid)
                // Add centered text on the line (if enabled)
                if divShowText
                    label.new(centerBarIndex, centerPrice, "DOWNTREND EXHAUSTION",
                             color=color.new(color.white, 100), textcolor=color.white, style=label.style_none, size=getTextSize())

            // Check for downtrend absorption: higher low in price but lower low in CVD
            if lastPriceLow.priceValue > prevPriceLow.priceValue and
               lastPriceLow.cvdValue < prevPriceLow.cvdValue
                // Calculate center position for text
                centerBarIndex = math.round((prevPriceLow.barIndex + lastPriceLow.barIndex) / 2)
                centerPrice = (prevPriceLow.priceValue + lastPriceLow.priceValue) / 2

                // Draw solid divergence line
                line.new(prevPriceLow.barIndex, prevPriceLow.priceValue, lastPriceLow.barIndex, lastPriceLow.priceValue,
                         color=color.white, width=2, style=line.style_solid)
                // Add centered text on the line (if enabled)
                if divShowText
                    label.new(centerBarIndex, centerPrice, "DOWNTREND ABSORPTION",
                             color=color.new(color.white, 100), textcolor=color.white, style=label.style_none, size=getTextSize())



// Volume Profile Calculation
type VolumeNode
    float price
    float volume
    float deltaVolume

var array<VolumeNode> volumeProfile = array.new<VolumeNode>()
var array<box> profileBoxes = array.new<box>()
var float maxVolumeProfile = 0.0
var float maxDeltaProfile = 0.0
var float priceStep = 0.0

// Calculate volume profile
if vpEnabled and barstate.islast
    // Clear previous profile data
    array.clear(volumeProfile)
    maxVolumeProfile := 0.0
    maxDeltaProfile := 0.0

    // Clear previous profile boxes to prevent snail trail
    if array.size(profileBoxes) > 0
        for i = 0 to array.size(profileBoxes) - 1
            box.delete(array.get(profileBoxes, i))
        array.clear(profileBoxes)

    // Get price range for lookback period
    var float highestHigh = high
    var float lowestLow = low

    for i = 0 to math.min(vpLookback - 1, bar_index)
        if i < bar_index
            highestHigh := math.max(highestHigh, high[i])
            lowestLow := math.min(lowestLow, low[i])

    // Calculate price step based on ticks per row (like TradingView)
    tickSize = syminfo.mintick
    priceStep := vpRowSize * tickSize

    // Calculate number of rows needed
    priceRange = highestHigh - lowestLow
    numRows = math.ceil(priceRange / priceStep)

    // Adjust the range to start from a round number
    startPrice = math.floor(lowestLow / priceStep) * priceStep

    // Initialize volume nodes
    for i = 0 to numRows - 1
        nodePrice = startPrice + (i + 0.5) * priceStep
        if nodePrice >= lowestLow and nodePrice <= highestHigh
            array.push(volumeProfile, VolumeNode.new(nodePrice, 0.0, 0.0))

    // Accumulate volume and delta for each price level
    for i = 0 to math.min(vpLookback - 1, bar_index)
        if i < bar_index
            barHigh = high[i]
            barLow = low[i]
            barVolume = volume[i]
            barDelta = lastVolume[i]  // Get delta volume for this bar

            // Distribute volume and delta across price levels within the bar's range
            for j = 0 to array.size(volumeProfile) - 1
                node = array.get(volumeProfile, j)
                if node.price >= barLow and node.price <= barHigh
                    // Distribute volume and delta evenly across all price levels in the bar
                    levelsInBar = math.max(1, math.round((barHigh - barLow) / priceStep))
                    node.volume += barVolume / levelsInBar
                    node.deltaVolume += barDelta / levelsInBar
                    array.set(volumeProfile, j, node)

    // Find maximum volume and delta for scaling
    for i = 0 to array.size(volumeProfile) - 1
        node = array.get(volumeProfile, i)
        if node.volume > maxVolumeProfile
            maxVolumeProfile := node.volume
        absDelta = math.abs(node.deltaVolume)
        if absDelta > maxDeltaProfile
            maxDeltaProfile := absDelta

// Draw Volume Profile
if vpEnabled and barstate.islast and array.size(volumeProfile) > 0
    // Calculate profile position - offset from latest bar
    currentBarIndex = bar_index
    profileAnchorX = currentBarIndex + vpOffset

    // Draw volume bars (inverted to face left) with mode-based sizing and coloring
    for i = 0 to array.size(volumeProfile) - 1
        node = array.get(volumeProfile, i)
        if node.volume > 0
            // Calculate bar width based on selected mode
            barWidth = if vpMode == "Delta Profile" and maxDeltaProfile > 0
                // Delta mode: width based on absolute delta pressure
                absDelta = math.abs(node.deltaVolume)
                math.round((absDelta / maxDeltaProfile) * vpProfileWidth)
            else if vpMode == "Volume Profile" and maxVolumeProfile > 0
                // Volume mode: width based on total volume
                math.round((node.volume / maxVolumeProfile) * vpProfileWidth)
            else
                0

            profileStartX = profileAnchorX - barWidth

            // Only draw if there's meaningful width
            if barWidth > 0
                // Determine color based on delta pressure (for both modes)
                binColor = if node.deltaVolume > 0
                    color.new(color.blue, 70)  // Positive delta = blue
                else if node.deltaVolume < 0
                    color.new(color.red, 70)   // Negative delta = red
                else
                    color.new(color.gray, 70)  // Neutral delta = gray

                // Draw volume bar (extends left from anchor position)
                newBox = box.new(profileStartX, node.price + (priceStep / 2), profileAnchorX, node.price - (priceStep / 2), bgcolor=binColor, border_color=color.new(binColor, 50), border_width=1)
                array.push(profileBoxes, newBox)

// Error handling for missing volume data
var cumVol = 0.
cumVol += nz(volume)
if barstate.islast and cumVol == 0
    runtime.error("The data vendor doesn't provide volume data for this symbol.")

// VWAP Plotting
plot(vwapValue, title = "VWAP", color = vwapColor, linewidth = vwapLineWidth, offset = vwapOffset)

upperBand_1 = plot(upperBandValue1, title="Upper Band #1", color = band1Color, offset = vwapOffset, display = showBand_1 ? display.all : display.none, editable = showBand_1)
lowerBand_1 = plot(lowerBandValue1, title="Lower Band #1", color = band1Color, offset = vwapOffset, display = showBand_1 ? display.all : display.none, editable = showBand_1)
fill(upperBand_1, lowerBand_1,      title="Bands Fill #1", color = color.new(band1Color, band1Transparency),   display = showBand_1 ? display.all : display.none, editable = showBand_1)

upperBand_2 = plot(upperBandValue2, title="Upper Band #2", color = band2Color, offset = vwapOffset, display = showBand_2 ? display.all : display.none, editable = showBand_2)
lowerBand_2 = plot(lowerBandValue2, title="Lower Band #2", color = band2Color, offset = vwapOffset, display = showBand_2 ? display.all : display.none, editable = showBand_2)
fill(upperBand_2, lowerBand_2,      title="Bands Fill #2", color = color.new(band2Color, band2Transparency),   display = showBand_2 ? display.all : display.none, editable = showBand_2)

upperBand_3 = plot(upperBandValue3, title="Upper Band #3", color = band3Color, offset = vwapOffset, display = showBand_3 ? display.all : display.none, editable = showBand_3)
lowerBand_3 = plot(lowerBandValue3, title="Lower Band #3", color = band3Color, offset = vwapOffset, display = showBand_3 ? display.all : display.none, editable = showBand_3)
fill(upperBand_3, lowerBand_3,      title="Bands Fill #3", color = color.new(band3Color, band3Transparency),   display = showBand_3 ? display.all : display.none, editable = showBand_3)
